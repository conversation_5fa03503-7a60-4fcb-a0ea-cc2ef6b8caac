# Image Prompt Extractor for E-commerce

An intelligent e-commerce image analysis tool that reverse-engineers competitor product images, extracts key visual elements and design features, and generates detailed AI drawing prompts.

## Project Overview

This project helps e-commerce operators and designers quickly analyze competitor images and generate detailed prompts for AI drawing tools, enabling efficient creation of similar-style product images. The tool specializes in fitness equipment analysis but can be extended to other product categories.

## Core Features

### 🔍 Advanced Image Analysis
- **Product Core Features**: Precise identification of fitness equipment types, specifications, material craftsmanship, brand elements, and product condition
- **Commercial Composition Strategy**: Visual focus analysis, spatial layout optimization, shooting angle selection, visual balance assessment, and commercial intent recognition
- **Professional Lighting Analysis**: Main light source direction and intensity, auxiliary lighting effects, material reflections, dimensional modeling, and atmospheric lighting
- **Background & Environment Design**: Background functionality, color coordination, spatial depth, commercial considerations, and technical implementation
- **Marketing Elements Extraction**: Text information, visual symbols, selling point emphasis, and emotional appeal analysis

### 🎨 Enhanced Visual Processing
- **Deep Material Analysis**: Detailed surface treatment identification (electroplating, painting, brushing, etc.)
- **Brand Recognition**: Logo, trademark, and brand identity positioning and styling analysis  
- **Quality Assessment**: New product display vs. usage scenarios, wear and usage trace detection
- **Commercial Intent Analysis**: Selling point highlighting techniques, specification comparisons, and usability demonstrations

### 📝 AI Prompt Generation
- **Structured Output**: Generate standardized AI drawing prompts in markdown code blocks for easy copying
- **Multi-Platform Support**: Compatible with Midjourney, DALL-E, Stable Diffusion, and Chinese AI tools
- **Enhanced Copyability**: All prompts formatted in code blocks with clear platform-specific sections
- **Comprehensive Parameters**: Includes negative prompts, technical parameters, and optimization suggestions
- **Bilingual Support**: Provides both English and Chinese versions optimized for different AI platforms

## Project Structure

```
├── 3.2 淘宝 健身器材店 作图方案/
│   └── 1. 图片逆向提示词生成器.md    # Main prompt template
├── .kiro/specs/image-prompt-extractor/  # Project specification documents
│   ├── requirements.md              # Requirements document
│   ├── design.md                   # Design document
│   └── tasks.md                    # Implementation plan
├── 3.商品图片/                      # Test image samples
├── 做出的新主图/                    # Generated result examples
└── Various prompt documents/        # Related prompt resources
```

## Usage

### Basic Usage
1. Copy the prompt template from `1. 图片逆向提示词生成器.md`
2. Send it along with the product image you want to analyze to ChatGPT, Claude, or other AI tools
3. The AI will automatically analyze the image and generate usable drawing prompts

### Advanced Features
- **Batch Processing**: Support simultaneous analysis of multiple competitor images
- **Template Customization**: Customize analysis templates for specific product types
- **Result Optimization**: Support fine-tuning and optimization of generated prompts

## Technical Features

### Analysis Dimensions
- **Product Characteristics**: Type, quantity, materials, shape, design details
- **Composition Layout**: Position, proportion, angle, perspective, visual hierarchy
- **Color & Lighting**: Primary colors, lighting effects, contrast, reflections
- **Background Environment**: Background type, tone, effects, spatial relationships
- **Text Elements**: Position, size, color, content extraction

### Output Format
- **Structured Analysis**: Clear categorization and hierarchical structure
- **Bilingual Support**: Provides both Chinese and English prompt versions
- **Parameter Recommendations**: Includes technical parameters and style settings
- **Quality Assurance**: Ensures commercial-grade image generation results

## Use Cases

- **E-commerce Operations**: Quickly replicate competitor main image styles
- **Designers**: Learn visual characteristics from excellent designs
- **Content Creation**: Batch generate similar-style product images
- **Market Analysis**: Analyze competitors' visual marketing strategies

## Project Status

The current project includes complete requirements analysis, system design, and implementation plan. Main components:

- ✅ Requirements Document - Fully defines 5 core requirements with acceptance criteria
- ✅ Design Document - Detailed system architecture and technical solutions
- ✅ Implementation Plan - 10-phase development task list with 40+ specific tasks
- ✅ Prompt Template - Ready-to-use analysis tool for immediate deployment
- 🔄 Implementation - Ready for development phase (see `.kiro/specs/` for detailed specifications)

## Tech Stack

- **Image Processing**: OpenCV, PIL, Segment Anything Model
- **Deep Learning**: YOLO v8, PaddleOCR, Transformers
- **Feature Extraction**: K-means clustering, texture analysis algorithms
- **Natural Language Processing**: Structured text generation, template engines

## Contributing

Welcome to submit Issues and Pull Requests to improve the project. Main improvement directions:

- Extend support for more product types
- Optimize feature extraction algorithm accuracy
- Add compatibility for more AI drawing tools
- Improve user interface and interaction experience

## License

This project is licensed under the MIT License - see the LICENSE file for details.

---

**Note**: This project focuses on e-commerce fitness equipment image analysis. For extension to other product types, please refer to the extension plans in the design document.