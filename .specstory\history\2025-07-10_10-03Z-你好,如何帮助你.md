<!-- Generated by SpecStory -->

# 你好，如何帮助你 (2025-07-10 10:03Z)

_**User**_

你好

---

_**Assistant**_

你好！有什么可以帮助你的吗？如果你有关于健身器材或其他商品的文案优化需求，或者有其他问题需要解答，请随时告诉我。

---

_**User**_

你好，如何在cursor中安装 mcp，需要填写 command，详细的mcp配置如下：【
{
  "mcpServers": {
    "chrome-mcp-server": {
      "type": "streamableHttp",
      "url": "http://127.0.0.1:12306/mcp"
    }
  }
}
】

---

_**Assistant**_

<thought>**Examining "mcp" Integration**

I'm currently working on understanding how to help the user integrate this "mcp" tool into Cursor. The JSON configuration provides initial clues about its "mcpServer" role. Further investigation is needed to clarify its exact function and how it interfaces with Cursor's architecture. I'll need to explore Cursor's extension mechanisms to provide effective integration instructions.


**Investigating "mcp" Integration**

I'm starting to understand the user's need.  The provided JSON snippet and the mention of a "command" suggest a custom tool setup within Cursor.  I'm interpreting "mcp" as a local server, perhaps connected to Chrome, based on its name and HTTP configuration. My next step is to formulate clear instructions that outline the likely Cursor configuration steps, including where to input the local server URL. I am thinking of including notes on basic troubleshooting steps.


**Formulating Configuration Steps**

I've clarified the user's intent. "mcp" appears to be a local server, likely related to Chrome, designed to be accessed via HTTP. The key challenge lies in deciphering the necessary command for Cursor's configuration. I now have a more concrete plan. I will outline the configuration steps: ensuring the user has the mcp server installed, determining the server startup command, and then explaining how to enter the command within Cursor's settings.


**Devising Installation Steps**

I'm now focusing on a plan for the user. I've broken down their needs and have identified the user's key challenge, which is to discover the startup command for the "mcp" server. I'm going to formulate a step-by-step guide. It will include verifying "mcp" server installation, determining the command to run it, and finally entering the command in Cursor's settings. I am going to search the web, using terms like "cursor ide mcp" and "cursor mcp server".


</thought>你好！看起来你正在尝试在 Cursor 中配置一个名为 "MCP"（Model-Copilot-Protocol）的自定义服务。这通常用于连接到本地运行的 AI 模型或其他后端服务。

根据你提供的配置，你需要一个命令来启动这个 `chrome-mcp-server`，它会在本地的 `12306` 端口上监听请求。

这个 `command` 字段需要填写的，是 **启动这个本地服务器的可执行文件或脚本的命令**。

这个服务器的具体启动命令取决于你是如何获得和安装 `mcp-server` 的。这里有几种常见的情况和对应的解决方案：

### 可能性一：你正在使用一个现成的 MCP 服务器项目

如果你从 GitHub 或其他地方克隆了一个项目（例如一个 Python 或 Node.js 的服务器），你需要用相应的语言环境去运行它。

*   **对于 Python 项目 (常见的):**
    如果你有一个 `server.py` 或 `main.py` 文件，启动命令可能是：
    ```bash
    python /path/to/your/project/server.py
    ```
    或者
    ```bash
    /path/to/your/python/venv/bin/python /path/to/your/project/server.py
    ```

*   **对于 Node.js 项目:**
    如果你有一个 `index.js` 或 `server.js` 文件，启动命令可能是：
    ```bash
    node /path/to/your/project/index.js
    ```

**你需要将 `/path/to/your/project/...` 替换成你本地存放该服务器代码的真实绝对路径。**

### 可能性二：你已经通过包管理器安装了它

如果你是通过 `pip`, `npm` 或其他包管理器全局安装的，那么它可能已经是一个可以直接执行的命令了。
在这种情况下，`command` 可能就是：
```bash
mcp-server
```
或者类似的可执行文件名。

### 总结和建议

1.  **确认 MCP 服务器已在运行**：在配置 Cursor 之前，请尝试手动在你的终端（例如 PowerShell 或 CMD）中运行你的启动命令，确保服务器可以成功启动，并且没有报错。
2.  **使用绝对路径**：在 Cursor 的 `command` 字段中，**强烈建议使用绝对路径** 来指定启动脚本或可执行文件，以避免因工作目录不同而导致的问题。
3.  **检查文档**：回顾一下你获取 `mcp-server` 的来源（比如它的 GitHub 页面），文档里通常会明确说明如何启动服务。

如果你能提供更多关于这个 `chrome-mcp-server` 的来源信息，我可以给出更精确的启动命令。

---

_**User**_

这个是json的格式的mcp，coomand命令如何写：【【【
{   "mcpServers": {     "chrome-mcp-server": {       "type": "streamableHttp",       "url": "http://127.0.0.1:12306/mcp"     }   } }
】】】

---

_**Assistant**_

你好，我明白你的问题了。你提供的 JSON 片段是 Cursor 用来 **连接** MCP 服务器的 **配置**，而不是用来 **启动** 服务器的。

所以，这里有一个重要的区别：

1.  **Cursor 的配置 (`settings.json` 或 `mcp.json`):**  这里告诉 Cursor：“嘿，有一个叫 `chrome-mcp-server` 的服务，你可以在 `http://127.0.0.1:12306/mcp` 这个地址找到它。” 你提供的 JSON 就是这个部分。
2.  **启动服务器的 `command`:** 这是另一个完全独立的东西。你需要一个 **实际的程序** 在后台运行，它会监听 `127.0.0.1:12306` 端口。`command` 字段的作用就是告诉 Cursor 如何启动这个程序。

**简单来说，你不能从这段 JSON 中直接得到 `command` 命令。** `command` 命令取决于 `chrome-mcp-server` 这个程序本身是什么以及它被安装在哪里。

### 如何找到正确的 `command`？

你需要回答以下问题：

*   **你是从哪里得到 `chrome-mcp-server` 的？**
    *   你是否从 GitHub 克隆了一个项目？如果是，项目是用什么语言写的（Python, Node.js, Go ...）？
    *   你是否下载了一个可执行文件（比如 `.exe` 或 `.sh`）？
    *   你是否通过像 `pip` 或 `npm` 这样的工具安装了它？

**根据不同的来源，`command` 会完全不同。**

---

#### 举个例子：

假设你从一个叫 `SuperMCP` 的 GitHub 仓库下载了一个 Python 项目，并且它的主文件是 `main.py`。你把它放在了 `D:\Tools\SuperMCP` 目录下。

那么，你需要做的有两件事：

1.  **手动启动服务器（用于测试）：**
    打开你的命令行工具 (PowerShell/CMD)，然后运行：
    ```bash
    python D:\Tools\SuperMCP\main.py
    ```
    如果它成功运行并没有报错，说明服务器是好的。

2.  **在 Cursor 中填写 `command`:**
    在 Cursor 需要填写 `command` 的地方，你就应该填入：
    ```
    python D:\Tools\SuperMCP\main.py
    ```

---

所以，请先确认 `chrome-mcp-server` 这个程序的来源，然后我们才能确定启动它的正确 `command`。如果你不确定，可以尝试在你的电脑里搜索 `chrome-mcp-server` 这个名字，看看能不能找到相关的文件或目录。

---

_**User**_

需要填写command命令，让cursor能够使用这个mcp。mcp的详细文档如下：{{{
Skip to content
Navigation Menu
hangwin
mcp-chrome

Type / to search
Code
Issues
34
Pull requests
4
Actions
Projects
Security
Insights
Owner avatar
mcp-chrome
Public
hangwin/mcp-chrome
Go to file
t
Name		
hangwin
hangwin
Merge pull request #86 from hangwin/fix/screenshot_fix
3af7fc1
 · 
2 days ago
.github/workflows
feat: Project Initialization and Code Migration.
last month
.husky
feat: Project Initialization and Code Migration.
last month
.vscode
feat: Project Initialization and Code Migration.
last month
app
fix: screenshot fix
2 days ago
docs
Update CONTRIBUTING.md
last week
packages
feat: add console tool & support mcp stdio type
3 weeks ago
prompt
Update excalidraw-prompt.md
last month
releases
fix: screenshot fix
2 days ago
.gitattributes
feat: Project Initialization and Code Migration.
last month
.gitignore
feat: Project Initialization and Code Migration.
last month
.prettierignore
feat: Project Initialization and Code Migration.
last month
.prettierrc.json
feat: Project Initialization and Code Migration.
last month
LICENSE
feat: Project Initialization and Code Migration.
last month
README.md
Update README.md
3 days ago
README_zh.md
Update README_zh.md
3 days ago
commitlint.config.cjs
feat: Project Initialization and Code Migration.
last month
eslint.config.js
feat: add inject tool
last month
package.json
feat: Project Initialization and Code Migration.
last month
pnpm-lock.yaml
feat: add console tool & support mcp stdio type
3 weeks ago
pnpm-workspace.yaml
feat: Project Initialization and Code Migration.
last month
test-inject-script.js
feat: add prompts for test
last month
Repository files navigation
README
MIT license
Chrome MCP Server 🚀  Chrome MCP 服务器 🚀
License: MIT TypeScript Chrome Extension

🌟 Turn your Chrome browser into your intelligent assistant - Let AI take control of your browser, transforming it into a powerful AI-controlled automation tool.
🌟 将您的 Chrome 浏览器变成您的智能助手 - 让 AI 控制您的浏览器，将其转变为强大的 AI 控制的自动化工具。

📖 Documentation: English | 中文
📖 文档 ： 英文 | 中文

The project is still in its early stages and is under intensive development. More features, stability improvements, and other enhancements will follow.
该项目仍处于早期阶段，正在紧锣密鼓地开发中。更多功能、稳定性改进和其他增强功能将陆续推出。

🎯 What is Chrome MCP Server?
🎯 什么是 Chrome MCP 服务器？
Chrome MCP Server is a Chrome extension-based Model Context Protocol (MCP) server that exposes your Chrome browser functionality to AI assistants like Claude, enabling complex browser automation, content analysis, and semantic search. Unlike traditional browser automation tools (like Playwright), Chrome MCP Server directly uses your daily Chrome browser, leveraging existing user habits, configurations, and login states, allowing various large models or chatbots to take control of your browser and truly become your everyday assistant.
Chrome MCP 服务器是一个基于 Chrome 扩展程序的模型上下文协议 （MCP） 服务器 ，可将您的 Chrome 浏览器功能公开给 Claude 等 AI 助手，从而实现复杂的浏览器自动化、内容分析和语义搜索。与传统的浏览器自动化工具（如 Playwright）不同，Chrome MCP Server 直接使用您日常的 Chrome 浏览器，利用现有的用户习惯、配置和登录状态，让各种大型模型或聊天机器人控制您的浏览器，真正成为您的日常助手。

✨ Core Features  ✨ 核心功能
😁 Chatbot/Model Agnostic: Let any LLM or chatbot client or agent you prefer automate your browser
😁 与聊天机器人/模型无关 ：让您喜欢的任何 LLM 或聊天机器人客户端或代理自动化您的浏览器
⭐️ Use Your Original Browser: Seamlessly integrate with your existing browser environment (your configurations, login states, etc.)
⭐️ 使用您的原始浏览器 ：与您现有的浏览器环境（您的配置、登录状态等）无缝集成
💻 Fully Local: Pure local MCP server ensuring user privacy
💻 完全本地 ：纯本地 MCP 服务器，确保用户隐私
🚄 Streamable HTTP: Streamable HTTP connection method
🚄 Streamable HTTP： 可流式传输的 HTTP 连接方法
🏎 Cross-Tab: Cross-tab context
🏎 交叉表 ：交叉表上下文
🧠 Semantic Search: Built-in vector database for intelligent browser tab content discovery
🧠 语义搜索 ：用于智能浏览器选项卡内容发现的内置矢量数据库
🔍 Smart Content Analysis: AI-powered text extraction and similarity matching
🔍 智能内容分析 ：AI 驱动的文本提取和相似度匹配
🌐 20+ Tools: Support for screenshots, network monitoring, interactive operations, bookmark management, browsing history, and 20+ other tools
🌐 20+ 工具 ：支持屏幕截图、网络监控、交互式作、书签管理、浏览历史和 20+ 其他工具
🚀 SIMD-Accelerated AI: Custom WebAssembly SIMD optimization for 4-8x faster vector operations
🚀 SIMD 加速 AI：自定义 WebAssembly SIMD 优化，可将矢量运算速度提高 4-8 倍
🆚 Comparison with Similar Projects
🆚 与类似项目的比较
Comparison Dimension  比较维度	Playwright-based MCP Server
基于 Playwright 的 MCP 服务器	Chrome Extension-based MCP Server
基于 Chrome 扩展的 MCP 服务器
Resource Usage  资源使用情况	❌ Requires launching independent browser process, installing Playwright dependencies, downloading browser binaries, etc.
❌ 需要启动独立的浏览器进程、安装 Playwright 依赖项、下载浏览器二进制文件等。	✅ No need to launch independent browser process, directly utilizes user's already open Chrome browser
✅ 无需启动独立的浏览器进程，直接利用用户已经打开的 Chrome 浏览器
User Session Reuse  用户会话重用	❌ Requires re-login  ❌ 需要重新登录	✅ Automatically uses existing login state
✅ 自动使用现有登录状态
Browser Environment  浏览器环境	❌ Clean environment lacks user settings
❌ Clean environment 缺少用户设置	✅ Fully preserves user environment
✅ 完全保留用户环境
API Access  API 访问	⚠️ Limited to Playwright API
⚠️ 仅限于 Playwright API	✅ Full access to Chrome native APIs
✅ 对 Chrome 原生 API 的完全访问权限
Startup Speed  启动速度	❌ Requires launching browser process
❌ 需要启动浏览器进程	✅ Only needs to activate extension
✅ 只需激活扩展
Response Speed  响应速度	50-200ms inter-process communication
50-200ms 进程间通信	✅ Faster  ✅ 更快
🚀 Quick Start  🚀 快速开始
Prerequisites  先决条件
Node.js >= 18.19.0 and pnpm/npm
Node.js >= 18.19.0 和 pnpm/npm
Chrome/Chromium browser  Chrome/Chromium 浏览器
Installation Steps  安装步骤
Download the latest Chrome extension from GitHub
从 GitHub 下载最新的 Chrome 扩展
Download link: https://github.com/hangwin/mcp-chrome/releases
下载地址： https://github.com/hangwin/mcp-chrome/releases

Install mcp-chrome-bridge globally
全局安装 mcp-chrome-bridge
npm

npm install -g mcp-chrome-bridge
pnpm

# Method 1: Enable scripts globally (recommended)
pnpm config set enable-pre-post-scripts true
pnpm install -g mcp-chrome-bridge

# Method 2: Manual registration (if postinstall doesn't run)
pnpm install -g mcp-chrome-bridge
mcp-chrome-bridge register
Note: pnpm v7+ disables postinstall scripts by default for security. The enable-pre-post-scripts setting controls whether pre/post install scripts run. If automatic registration fails, use the manual registration command above.
注意：为了安全起见，pnpm v7+ 默认禁用 postinstall 脚本。enable-pre-post-scripts 设置控制是否运行安装前/安装后脚本。如果自动注册失败，请使用上面的手动注册命令。

Load Chrome Extension  加载 Chrome 扩展程序
Open Chrome and go to chrome://extensions/
打开 Chrome 并转到 chrome://extensions/
Enable "Developer mode"  启用“开发人员模式”
Click "Load unpacked" and select your/dowloaded/extension/folder
点击 “Load unpacked” 并选择 your/dowloaded/extension/folder
Click the extension icon to open the plugin, then click connect to see the MCP configuration Screenshot 2025-06-09 15 52 06
单击扩展图标以打开插件，然后单击 connect 以查看 MCP 配置 Screenshot 2025-06-09 15 52 06
Usage with MCP Protocol Clients
与 MCP 协议客户端一起使用
Using Streamable HTTP Connection (👍🏻 Recommended)
使用可流式传输的 HTTP 连接（👍🏻推荐）
Add the following configuration to your MCP client configuration (using CherryStudio as an example):
将以下配置添加到您的 MCP 客户端配置中（以 CherryStudio 为例）：

Streamable HTTP connection method is recommended
建议使用可流式传输的 HTTP 连接方法

{
  "mcpServers": {
    "chrome-mcp-server": {
      "type": "streamableHttp",
      "url": "http://127.0.0.1:12306/mcp"
    }
  }
}
Using STDIO Connection (Alternative)
使用 STDIO 连接（备选）
If your client only supports stdio connection method, please use the following approach:
如果您的客户端只支持 stdio 连接方式，请使用以下方法：

First, check the installation location of the npm package you just installed
首先，检查您刚刚安装的 npm 包的安装位置
# npm check method
npm list -g mcp-chrome-bridge
# pnpm check method
pnpm list -g mcp-chrome-bridge
Assuming the command above outputs the path: /Users/<USER>/Library/pnpm/global/5 Then your final path would be: /Users/<USER>/Library/pnpm/global/5/node_modules/mcp-chrome-bridge/dist/mcp/mcp-server-stdio.js
假设上面的命令输出路径： /Users/<USER>/Library/pnpm/global/5 那么您的最终路径将是： /Users/<USER>/Library/pnpm/global/5/node_modules/mcp-chrome-bridge/dist/mcp/mcp-server-stdio.js

Replace the configuration below with the final path you just obtained
将下面的配置替换为您刚刚获得的最终路径
{
  "mcpServers": {
    "chrome-mcp-stdio": {
      "command": "npx",
      "args": [
        "node",
        "/Users/<USER>/Library/pnpm/global/5/node_modules/mcp-chrome-bridge/dist/mcp/mcp-server-stdio.js"
      ]
    }
  }
}
eg：config in augment:  eg：config 的 Config 中：

截屏2025-06-22 22 11 25
🛠️ Available Tools  🛠️ 可用工具
Complete tool list: Complete Tool List
Complete tool list： 完整工具列表

📊 Browser Management (6 tools)
📊 浏览器管理 （6 工具）
get_windows_and_tabs - List all browser windows and tabs
get_windows_and_tabs - 列出所有浏览器窗口和选项卡
chrome_navigate - Navigate to URLs and control viewport
chrome_navigate - 导航到 URL 和控制视区
chrome_close_tabs - Close specific tabs or windows
chrome_close_tabs - 关闭特定选项卡或窗口
chrome_go_back_or_forward - Browser navigation control
chrome_go_back_or_forward - 浏览器导航控件
chrome_inject_script - Inject content scripts into web pages
chrome_inject_script - 将内容脚本注入网页
chrome_send_command_to_inject_script - Send commands to injected content scripts
chrome_send_command_to_inject_script - 将命令发送到注入的内容脚本
📸 Screenshots & Visual (1 tool)
📸 屏幕截图和视觉（1个工具）
chrome_screenshot - Advanced screenshot capture with element targeting, full-page support, and custom dimensions
chrome_screenshot - 具有元素定位、整页支持和自定义维度的高级屏幕截图捕获
🌐 Network Monitoring (4 tools)
🌐 网络监控 （4 工具）
chrome_network_capture_start/stop - webRequest API network capture
chrome_network_capture_start/stop - webRequest API 网络捕获
chrome_network_debugger_start/stop - Debugger API with response bodies
chrome_network_debugger_start/stop - 带有响应正文的调试器 API
chrome_network_request - Send custom HTTP requests
chrome_network_request - 发送自定义 HTTP 请求
🔍 Content Analysis (4 tools)
🔍 内容分析 （4 工具）
search_tabs_content - AI-powered semantic search across browser tabs
search_tabs_content - 跨浏览器选项卡的 AI 驱动的语义搜索
chrome_get_web_content - Extract HTML/text content from pages
chrome_get_web_content - 从页面中提取 HTML/文本内容
chrome_get_interactive_elements - Find clickable elements
chrome_get_interactive_elements - 查找可点击的元素
chrome_console - Capture and retrieve console output from browser tabs
chrome_console - 从浏览器选项卡捕获和检索控制台输出
🎯 Interaction (3 tools)  🎯 交互 （3 个工具）
chrome_click_element - Click elements using CSS selectors
chrome_click_element - 使用 CSS 选择器单击元素
chrome_fill_or_select - Fill forms and select options
chrome_fill_or_select - 填写表单并选择选项
chrome_keyboard - Simulate keyboard input and shortcuts
chrome_keyboard - 模拟键盘输入和快捷键
📚 Data Management (5 tools)
📚 数据管理 （5 工具）
🧪 Usage Examples  🧪 使用示例
AI helps you summarize webpage content and automatically control Excalidraw for drawing
AI 帮你总结网页内容，自动控制 Excalidraw 绘图
prompt: excalidraw-prompt Instruction: Help me summarize the current page content, then draw a diagram to aid my understanding.
提示： excalidraw-prompt 说明：帮我总结当前页面的内容，然后画一个图表来帮助我理解。

 use.chrome-mcp.to.summarize.web.content.and.then.draw.with.Excalidraw.mp4 
After analyzing the content of the image, the LLM automatically controls Excalidraw to replicate the image
分析图像内容后，LLM 会自动控制 Excalidraw 复制图像
prompt: excalidraw-prompt|content-analize Instruction: First, analyze the content of the image, and then replicate the image by combining the analysis with the content of the image.
提示： excalidraw-prompt| 内容分析 说明：首先，分析图像的内容，然后通过将分析与图像内容相结合来复制图像。

 chrome-mcpa.ae.a.a.c.a.a.a.c.a.mp4 
AI automatically injects scripts and modifies webpage styles
AI 自动注入脚本并修改网页样式
prompt: modify-web-prompt Instruction: Help me modify the current page's style and remove advertisements.
提示符：modify-web-prompt 说明：帮我修改当前页面的样式并删除广告。

 use.chrome-mcp.to.modify.web.style.mp4 
AI automatically captures network requests for you
AI 会自动为您捕获网络请求
query: I want to know what the search API for Xiaohongshu is and what the response structure looks like
query：我想知道小红书的搜索 API 是什么，响应结构是什么样的

 rednote-low.mp4 
AI helps analyze your browsing history
AI 帮助分析您的浏览历史记录
query: Analyze my browsing history from the past month
query： 分析我过去一个月的浏览历史

 history.mp4 
Web page conversation  网页对话
query: Translate and summarize the current web page
query：翻译和汇总当前网页

 webcontent.mp4 
AI automatically takes screenshots for you (web page screenshots)
AI 会自动为您截取屏幕截图（网页屏幕截图）
query: Take a screenshot of Hugging Face's homepage
query： 截取 Hugging Face 的主页

 screenshot_full.mp4 
AI automatically takes screenshots for you (element screenshots)
AI 会自动为您截取屏幕截图（元素屏幕截图）
query: Capture the icon from Hugging Face's homepage
query： 从 Hugging Face 的首页捕获图标

 screenshot_logo.mp4 
AI helps manage bookmarks
AI 帮助管理书签
query: Add the current page to bookmarks and put it in an appropriate folder
query：将当前页面添加到书签中，并将其放入适当的文件夹中

 bookmark-low.mp4 
Automatically close web pages
自动关闭网页
query: Close all shadcn-related web pages
query：关闭所有与 shadcn 相关的网页

 close_tab.mp4 
🤝 Contributing  🤝 贡献
We welcome contributions! Please see CONTRIBUTING.md for detailed guidelines.
我们欢迎贡献！请参阅 CONTRIBUTING.md 了解详细指南。

🚧 Future Roadmap  🚧 未来路线图
We have exciting plans for the future development of Chrome MCP Server:
我们对 Chrome MCP 服务器的未来发展有着令人兴奋的计划：

 Authentication  认证
 Recording and Playback
录制和播放
 Workflow Automation  工作流程自动化
 Enhanced Browser Support (Firefox Extension)
增强的浏览器支持（Firefox 扩展）
Want to contribute to any of these features? Check out our Contributing Guide and join our development community!
想要为这些功能做出贡献吗？ 查看我们的贡献指南并加入我们的开发社区！

📄 License  📄 许可证
This project is licensed under the MIT License - see the LICENSE file for details.
此项目根据 MIT 许可证获得许可 - 有关详细信息，请参阅 LICENSE 文件。

📚 More Documentation  📚 更多文档
Architecture Design - Detailed technical architecture documentation
架构设计 - 详细的技术架构文档
TOOLS API - Complete tool API documentation
TOOLS API - 完整的工具 API 文档
Troubleshooting - Common issue solutions
故障排除 - 常见问题解决方案
About
Chrome MCP Server is a Chrome extension-based Model Context Protocol (MCP) server that exposes your Chrome browser functionality to AI assistants like Claude, enabling complex browser automation, content analysis, and semantic search.
Chrome MCP 服务器是一个基于 Chrome 扩展程序的模型上下文协议 （MCP） 服务器，可将您的 Chrome 浏览器功能公开给 Claude 等 AI 助手，从而实现复杂的浏览器自动化、内容分析和语义搜索。

Resources
 Readme
License
 MIT license
 Activity
Stars
 3.1k stars
Watchers
 11 watching
Forks
 226 forks
Report repository
Releases 6
v0.0.6
Latest
2 days ago
+ 5 releases
Packages
No packages published
Contributors
4
@hangwin
hangwin
@claude
claude Claude
@aaron13100
aaron13100 Aaron
@wyhao31
wyhao31 Yanhao Wang
Languages
TypeScript
58.0%
 
JavaScript
31.4%
 
Vue
7.7%
 
Rust
1.0%
 
Shell
0.9%
 
CSS
0.5%
 
Other
0.5%
Footer
© 2025 GitHub, Inc.
Footer navigation
Terms
Privacy
Security
Status
Docs
Contact
Manage cookies
Do not share my personal information
Copied! 

}}}

---

_**Assistant**_

<thought>**Investigating Command Configuration**

I'm currently focused on the `command` field within the MCP server configuration. I've been reviewing the README.md file from `hangwin/mcp-chrome` to extract the crucial information needed for setup. Specifically, I'm attempting to decipher the optimal command structure to ensure proper communication between the MCP server and the Chrome environment.


**Parsing Connection Details**

I've carefully analyzed the "Quick Start" section and the available connection methods.  The "Streamable HTTP Connection" seems the preferred route, as it doesn't require a specific `command` entry. I noted its instructions, including the  `npm install -g mcp-chrome-bridge` step and the suggested JSON configuration with the `url` key pointing to `http://127.0.0.1:12306/mcp`. This seems to sidestep the need for a command, relying instead on the Chrome extension and the bridge for communication.


**Confirming Streamable Connection**

After reviewing the README, I'm confident the `streamableHttp` connection method is preferable. It eliminates the need for a command, relying instead on the `mcp-chrome-bridge` and Chrome extension. Setting the `url` key to `http://127.0.0.1:12306/mcp` seems sufficient for establishing communication. This streamlined approach aligns with the suggested "Quick Start" instructions.




---

