# 6

拼多多上架

目前上架到 17个，要上架 18了

用 gemini 做标题
图片 用 闲鱼的图片
文案用 闲鱼的文案 （1.7 优化后的闲鱼文案）
价格 用（1.5 整理排版后的商品信息）中的价格



# 4
1.修改好了文案和价格
2.翻转好了图片
3.根据原图 做主图 33个做好了，将要做 34个

之后：
做好主图，修正下 图片中的乱码

然后 上架，上传 图片和文案。

上传到 37个了，要上传 38
 上完了



# 3

修改主图
修改图片中文字的乱码，用 canva
目前到 第 20个：{20. 饰硌杠铃杆}




# 2

上架 到 第7个商品，将要（### **商品 8/24**）个，然后上传其他的 供货商新上架的

1. 明天继续上传，争取上午上传完 目前做好的
2. 然后通知供货商，做拼多多
3. 然后 再做
新上架的，做下文案和修改图片，再次上传



# 闲鱼商品上架标准作业流程 (SOP)

本文档旨在规范化闲鱼店铺的商品上架流程，确保效率和一致性。

---

### **第一步：商品选择与定价**

1.  **商品来源**：
    *   `铁悍206仓库`

2.  **定价原则**：
    *   在供货商原价基础上，增加 **5元至30元** 不等的利润。
    *   **调价比例参考**：
        *   例如：原价 `300元` 的商品，可定价为 `330元`。
        *   例如：原价 `20元` 的商品，可定价为 `25元`。

---

### **第二步：原始素材采集**

1.  **搜集同款**：
    *   在闲鱼上寻找已有的、销量或热度较高的同款商品链接。

2.  **采集文案**：
    *   复制其完整的商品文案，包含标题、描述、参数、价格等信息。
    *   将文案粘贴并保存到 Cursor 的一个新建 `.md` 文件中，作为【原始文案】。

3.  **采集图片**：
    *   从该商品页面下载所有图片。
    *   将图片保存到本地的专用图片文件夹。

---

### **第三步：文案与图片优化处理**

1.  **文案优化 (核心)**：
    *   **执行动作**：调用我们优化好的 **"闲鱼文案改写优化专家"** 提示词 (Prompt)。
    *   **输入**：将第二步采集的【原始文案】作为输入。
    *   **输出**：获得三份【精简直接】风格的新文案，供后续选用。

2.  **图片处理**：
    *   **① 图片翻转**：
        *   将所有采集到的图片进行**水平翻转**处理。
    *   **② 文字修正 (如需)**：
        *   部分图片翻转后，上面的文字会变成镜像乱码。此时需要进行修正。
        *   **执行动作**：使用 AI 图像编辑功能。
        *   **参考提示词 (Prompt)**：
            ```prompt
            {{{

**图像编辑说明：**

* **任务：** 将图像上方的翻转文字替换为正常的文字内容
* **原文内容：** 翻转文字（现为：“量仝\耂悙\弍耂 息屮合忄” 等乱码样）
* **修改后内容：** `硬拉 / 卧推 / 力量举，综合训练`
* **风格要求：**

  * 保持原文字区域的色彩风格（彩色渐变、可爱卡通风格）
  * 保持背景、产品构图、图像比例不变
  * 保持周围点缀图案（如小星星、边框线）不变

            }}}
            ```

---

### **第四步：上架发布**

1.  **整合素材**：
    *   从三份新文案中挑选最满意的一份。
    *   准备好所有处理完毕的图片。

2.  **填写上架信息**：
    *   **价格**：按照第一步的定价原则填写。
    *   **品牌**：选择 `其他`。
    *   **发货地**：选择 `云南` `曲靖`。
    .
