# Implementation Plan

- [ ] 1. Set up project structure and core interfaces
  - Create directory structure for components, templates, and utilities
  - Define TypeScript interfaces for AnalysisResult and PromptOutput data models
  - Set up basic project configuration files (package.json, tsconfig.json)
  - _Requirements: 1.1, 2.1, 3.1_

- [ ] 2. Implement image analysis core functionality
  - [ ] 2.1 Create image processing utilities
    - Write image upload and validation functions
    - Implement basic image metadata extraction
    - Create image format conversion utilities
    - _Requirements: 1.1, 1.2_

  - [ ] 2.2 Implement product identification system
    - Code product type detection for fitness equipment
    - Write quantity counting and arrangement analysis
    - Implement material characteristic extraction
    - _Requirements: 1.1, 1.2_

  - [ ] 2.3 Build composition analysis module
    - Create layout and positioning detection functions
    - Implement viewing angle classification
    - Write spatial relationship analysis code
    - _Requirements: 2.1, 2.2_

- [ ] 3. Develop color and lighting analysis
  - [ ] 3.1 Implement color extraction system
    - Write primary color detection algorithms
    - Create background tone analysis functions
    - Implement contrast relationship calculations
    - _Requirements: 2.3_

  - [ ] 3.2 Build lighting analysis module
    - Code light direction detection
    - Implement highlight and shadow identification
    - Write reflection effect analysis
    - _Requirements: 2.3_

- [ ] 4. Create text recognition capabilities
  - [ ] 4.1 Implement OCR functionality
    - Integrate text detection library
    - Write Chinese and English text extraction
    - Create text positioning and styling analysis
    - _Requirements: 4.1, 4.2, 4.3_

  - [ ] 4.2 Build text analysis system
    - Code product name and selling point extraction
    - Implement text element classification
    - Write text-to-prompt conversion logic
    - _Requirements: 4.2, 4.3_

- [ ] 5. Develop prompt generation system
  - [ ] 5.1 Create platform-specific prompt generators
    - Write Midjourney prompt formatter with parameter support
    - Implement DALL-E prompt structure
    - Create Stable Diffusion positive/negative prompt system
    - _Requirements: 3.1, 3.2, 5.1, 5.2_

  - [ ] 5.2 Build Chinese AI tool prompt generator
    - Code Chinese prompt formatting for domestic tools
    - Implement cultural and linguistic adaptations
    - Write platform-specific syntax handling
    - _Requirements: 5.3, 5.4_

  - [ ] 5.3 Implement negative prompt generation
    - Create negative prompt templates for common issues
    - Write logic to avoid blur, distortion, unwanted objects
    - Implement platform-specific negative prompt formatting
    - _Requirements: 3.2_

- [ ] 6. Create output formatting system
  - [ ] 6.1 Implement markdown code block formatter
    - Write markdown generation utilities
    - Create platform-specific code block sections
    - Implement copy-friendly formatting structure
    - _Requirements: 1.4, 3.4, 5.2_

  - [ ] 6.2 Build template management system
    - Create prompt template storage and retrieval
    - Implement template customization capabilities
    - Write template validation and error handling
    - _Requirements: 3.1, 3.4_

- [ ] 7. Integrate analysis and generation components
  - [ ] 7.1 Create main processing pipeline
    - Write end-to-end image processing workflow
    - Implement data flow from analysis to prompt generation
    - Create error handling and fallback mechanisms
    - _Requirements: 1.1, 1.2, 1.3, 1.4_

  - [ ] 7.2 Build output coordination system
    - Code multi-platform prompt generation coordination
    - Implement output formatting and organization
    - Write final result compilation and presentation
    - _Requirements: 3.3, 3.4, 5.1, 5.2_

- [ ] 8. Implement error handling and validation
  - [ ] 8.1 Create input validation system
    - Write image file validation and sanitization
    - Implement file size and format checking
    - Create error messaging for invalid inputs
    - _Requirements: 1.1_

  - [ ] 8.2 Build analysis error handling
    - Code fallback mechanisms for failed analysis
    - Implement graceful degradation for unclear images
    - Write default prompt generation for edge cases
    - _Requirements: 1.2, 2.1, 2.2, 2.3_

- [ ] 9. Create comprehensive test suite
  - [ ] 9.1 Write unit tests for core components
    - Create tests for image analysis functions
    - Write prompt generation validation tests
    - Implement output formatting verification tests
    - _Requirements: 1.1, 1.2, 1.3, 1.4_

  - [ ] 9.2 Build integration tests
    - Write end-to-end workflow tests
    - Create platform compatibility verification tests
    - Implement multi-language output validation tests
    - _Requirements: 3.1, 3.2, 3.3, 3.4, 5.1, 5.2, 5.3, 5.4_

- [ ] 10. Optimize and finalize implementation
  - [ ] 10.1 Performance optimization
    - Optimize image processing algorithms for speed
    - Implement caching for repeated operations
    - Write efficient template rendering system
    - _Requirements: 1.1, 1.2_

  - [ ] 10.2 Final integration and polish
    - Integrate all components into cohesive system
    - Write comprehensive documentation and examples
    - Create user-friendly interface for the prompt generator
    - _Requirements: 1.4, 3.4, 5.2_