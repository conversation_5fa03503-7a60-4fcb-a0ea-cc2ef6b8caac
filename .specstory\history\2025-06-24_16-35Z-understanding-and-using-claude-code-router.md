<!-- Generated by SpecStory -->

# Understanding and using claude-code-router (2025-06-24 16:35Z)

_**User**_

看下这个文档，是什么，我要如何使用：{{{
@https://github.com/musistudio/claude-code-router 

---

_**Assistant**_

<thought>**Reviewing Project Scope**

I'm currently focused on the initial project summary. I've been examining the `claude-code-router` repository to understand its purpose and how it functions. My initial assessment centers on clarifying its role as a proxy and router for AI coding assistants, particularly with a focus on "Claude Code." I aim to provide a concise overview of the project's key features and functionalities.


**Analyzing Configuration Options**

I've been delving deeper into the configuration aspects of `claude-code-router`.  Understanding the `config.json` file is key.  I'm unpacking how it handles different API keys for various providers and how it specifies the available models.  The aim is to articulate the setup process clearly, highlighting the flexibility in integrating diverse LLMs.


**Understanding Routing Mechanisms**

I'm now focusing on how the routing within `claude-code-router` actually functions. I'm exploring the logic behind the `Router` section in the `config.json` file. My goal is to clearly explain how task types are matched to specific providers and models. I plan to detail how the priority system works for selecting models and how this architecture makes routing efficient.


**Deciphering Core Functionality**

I'm now focusing on a step-by-step guide to using `claude-code-router`. I plan to articulate its core functionality clearly. The aim is to clarify how users can leverage the routing capabilities to optimize their AI coding workflows, including the initial setup, configuration via `config.json`, and integration with tools like GitHub Actions. My goal is to facilitate its use in real-world scenarios.


**Elaborating on Functionality**

I'm now focusing on a more granular explanation of `claude-code-router`. The setup process requires a `config.json` file where API keys for different providers (OpenAI, Anthropic, etc.) are declared, alongside model specifications. The Router section is key, defining rules to map tasks like 'background' or 'think' to specific model configurations, enabling optimization and cost savings. I will delve into specific use cases to enhance comprehension.


**Summarizing Core Features**

I'm now focusing on a succinct summary of `claude-code-router`. This tool acts as a proxy, routing requests to various LLMs for coding tasks. The goal is cost-effectiveness and flexibility by leveraging diverse models for different tasks. This can be accomplished through a well-defined `config.json` file.


**Outlining Core Functionality**

I'm now focusing on a step-by-step guide to using `claude-code-router`. The focus will be its routing capabilities and how users can optimize their AI coding workflows. This includes initial setup, configuration via `config.json`, and integration with tools like GitHub Actions. The aim is to facilitate its use in real-world scenarios. The Chinese translation will be included, as requested.

\n\n

**构建核心功能指南**

现在，我将重点关注使用 `claude-code-router` 的分步指南。重点在于它的路由功能以及用户如何优化他们的 AI 编码工作流程。这包括初始设置、通过 `config.json` 的配置以及与 GitHub Actions 等工具的集成。目标是促进它在实际应用场景中的使用。按照要求，将包括中文翻译。


</thought>好的，根据您提供的 GitHub 仓库链接，我来为您解释一下这个项目是什么以及如何使用它。

### 这个项目是什么？

这个项目叫做 **`claude-code-router`**，它是一个非常巧妙的工具，可以看作是一个为 AI 编程助手设计的“智能路由”或“代理服务”。

它的核心思想是：利用 Anthropic 公司强大的 `claude-code` 作为编程能力的基础框架，但允许您**灵活地决定由哪个大语言模型（LLM）来处理具体的编码请求**。

**主要解决的问题和带来的好处是：**

1.  **成本效益**：像 Claude 3.5 Sonnet 或 GPT-4o 这样的顶级模型虽然强大，但使用成本较高。这个工具允许您将不同的任务路由到更便宜甚至免费的模型上（例如 DeepSeek、或者通过 Ollama 在本地运行的开源模型），从而以极低的成本使用 Claude 级别的编码能力。
2.  **灵活性和专业化**：不同的模型有不同的优势。您可以配置将需要复杂推理（`think`）的任务交给一个模型，将需要处理超长上下文（`longContext`）的任务交给另一个模型，而将一些简单的后台任务（`background`）交给一个轻量级模型。
3.  **绕过限制**：您可以利用这个工具接入不同供应商的 API，绕过特定模型的速率限制或上下文窗口限制。

总而言之，它让您在使用类似 Claude Code 的先进编码工具时，拥有了更高的自由度和更低的成本。

### 我要如何使用它？

根据文档描述，使用这个项目主要分为以下几个步骤：

#### 1. 运行路由服务

您需要在您的机器上（可以是本地电脑或服务器）以后台服务的形式运行 `claude-code-router`。文档中提到了使用 `bun` 来运行的命令：

```bash
# 这条命令会下载并启动路由服务
nohup ~/.bun/bin/bunx @musistudio/claude-code-router@1.0.8 start &
```

这个服务会默认在本地的 `3456` 端口监听请求。

#### 2. 创建并编辑配置文件

这是最关键的一步。您需要在您的用户主目录下创建一个名为 `.claude-code-router` 的文件夹，并在其中创建一个 `config.json` 文件。

路径示例：
*   Linux/macOS: `~/.claude-code-router/config.json`
*   Windows: `C:\Users\<USER>\.claude-code-router\config.json`

这个 `config.json` 文件定义了所有规则。一个典型的配置如下：

```json
{
  "log": true, // 是否开启日志
  // 定义可用的模型提供商
  "Providers": [
    {
      "provider": "deepseek", // 提供商名称
      "api_base_url": "https://api.deepseek.com/v1", // API 地址
      "api_key": "YOUR_DEEPSEEK_API_KEY", // 你的 API Key
      "models": ["deepseek-reasoner", "deepseek-chat"] // 该提供商下的模型
    },
    {
      "provider": "openrouter",
      "api_base_url": "https://openrouter.ai/api/v1",
      "api_key": "YOUR_OPENROUTER_API_KEY",
      "models": ["google/gemini-2.5-pro-preview"]
    },
    {
      "provider": "ollama", // 本地 Ollama 服务
      "api_base_url": "http://localhost:11434/v1",
      "api_key": "ollama",
      "models": ["qwen2.5-coder:latest"]
    }
  ],
  // 定义路由规则
  "Router": {
    // 后台任务使用本地的 qwen 模型
    "background": "ollama,qwen2.5-coder:latest", 
    // "思考"和"计划"任务使用 deepseek-reasoner 模型
    "think": "deepseek,deepseek-reasoner",
    // 长文本任务使用 Gemini 2.5 Pro
    "longContext": "openrouter,google/gemini-2.5-pro-preview"
  }
}
```

#### 3. 将您的 AI 工具指向路由服务

现在，您需要将原本直接调用 Anthropic API 的工具，改为调用您在本地启动的 `claude-code-router` 服务。

文档中给出的主要示例是集成 **GitHub Actions**：

1.  在您的仓库中安装官方的 `Claude Code Actions`。
2.  修改仓库中的 `.github/workflows/claude.yaml` 文件。
3.  最关键的改动是，将 `env` 部分的 `ANTHROPIC_BASE_URL` 指向您的本地路由服务地址 `http://localhost:3456`。

```yaml
// ... (其他配置) ...
      - name: Run Claude Code
        id: claude
        uses: anthropics/claude-code-action@beta
        env:
          # 将 API 地址指向你的路由服务
          ANTHROPIC_BASE_URL: http://localhost:3456 
        with:
          # API Key 可以填任意字符串，因为真正的验证在你的路由服务和config.json中完成
          anthropic_api_key: "test" 
```

当这个 GitHub Action 运行时，它会向 `http://localhost:3456` 发送请求，`claude-code-router` 会拦截这个请求，并根据您的 `config.json` 规则，将其转发给最终的 LLM（例如 DeepSeek）。

#### 4. (可选) 使用插件

项目还支持插件系统，可以用来修改请求参数或过滤工具调用。例如：

*   `notebook-tools-filter`: 如果你不用 Jupyter Notebook，可以用它过滤掉相关的工具。
*   `toolcall-improvement`: 如果你使用的模型对工具调用支持不佳，这个插件可以通过添加特定的系统提示词来优化它。

希望这个解释对您有帮助！

***
*信息来源: [musistudio/claude-code-router on GitHub](https://github.com/musistudio/claude-code-router)*

---

_**User**_

如何安装和使用

---

_**Assistant**_

<thought>**Outlining Installation Steps**

I'm currently fleshing out the installation and usage instructions for the `claude-code-router` project. Based on the README, I'm outlining a clear, step-by-step guide for installation, ensuring even non-technical users can follow along. I'm focusing on simplicity and clarity in the instructions.


**Clarifying Prerequisites & Steps**

I've outlined the installation process, focusing on clarity. I'm now detailing prerequisites, especially `bun`, and providing the installation command. Moving into service setup, I'll explain `bunx` and configuration with `config.json`, including its creation location across different OSes and key sections like "Providers" and "Router". I'll also stress replacing placeholder API keys. Finally, I'll briefly cover usage and connecting clients, especially GitHub Actions.


**Structuring the Installation Guide**

I'm now diving into the specifics of the installation guide. I'll outline each crucial step: setting up the prerequisites (specifically, installing `bun`), running the service, configuring the `config.json` file (including providing paths for different OSes and highlighting key sections), and briefly covering usage, with GitHub Actions as the primary example. I'm focusing on simplicity and making the guide accessible to all users.


**Developing Configuration Guidance**

I'm now detailing the configuration process. First, I'll guide users on where to place the `config.json` file, specifying paths for different OSes. I'll provide a comprehensive, commented template, clarifying each section, especially the "Providers" and "Router" sections, emphasizing the need to input their API keys. I will also clarify the use of Ollama locally. Lastly, I'll explain the `background`, `think` and `longContext` options.


**Detailing the Setup Process**

Now, I'm focusing on providing comprehensive installation instructions. I'm outlining how to install `bun`, starting with distinct commands for both Linux/macOS and Windows users. I'll break down the command for running the service and explain the importance of the `nohup` and `bunx` elements. Following that, I'll provide detailed guidance on configuring the `config.json` file, explaining its creation and key sections, and offer a clear GitHub Actions example, highlighting the vital requirement for a self-hosted runner.


**Constructing the Usage Guide**

I'm now detailing the installation and usage instructions. Firstly, I'll guide on installing `bun`, providing distinct commands for Linux/macOS and Windows. I'll explain running the `claude-code-router` service using `bunx` with `nohup`, followed by `config.json` setup, detailing where to place it for each OS. I'll provide a clear, commented `config.json` template, emphasizing API key entry. Finally, I'll offer a GitHub Actions example for integration, highlighting the requirement for secrets within the repository settings.


**Outlining the Setup Process**

Now, I'm focusing on providing comprehensive and actionable installation instructions.  First, users need `bun`, which can be installed with `curl -fsSL https://bun.sh/install | bash` for Linux/macOS, or `powershell -c "irm bun.sh/install.ps1|iex"` for Windows. Next, I'll clarify how to run the service using `nohup ~/.bun/bin/bunx @musistudio/claude-code-router@1.0.8 start &`, explaining each part of the command.  I'll then guide users through configuring `config.json`, detailing paths for different OSes and the critical "Providers" and "Router" sections. I'll also clarify the use of Ollama for local model support and explain key options such as the use of `background`, `think` and `longContext`. I will then outline the GitHub Actions example, highlighting the importance of setting secrets within the repository settings.


</thought>好的，我们来分步说明如何安装和使用 `claude-code-router`。整个过程可以分为三个主要步骤：**准备环境**、**启动和配置路由服务**、以及**在具体场景中使用**。

由于这个项目是基于 `bun` 的，所以我们先从安装 `bun` 开始。

---

### 第 1 步：准备环境 (安装 Bun)

`Bun` 是一个现代化的 JavaScript 运行时，类似于 Node.js，但速度更快。您需要先在您的系统上安装它。

打开您的终端（在 Windows 上是 PowerShell），然后运行以下对应的命令：

*   **对于 macOS / Linux / WSL:**
    ```bash
    curl -fsSL https://bun.sh/install | bash
    ```

*   **对于 Windows (在 PowerShell 中运行):**
    ```powershell
    powershell -c "irm bun.sh/install.ps1|iex"
    ```
安装完成后，关闭并重新打开您的终端，以确保 `bun` 命令可用。

---

### 第 2 步：启动并配置路由服务

这一步是核心。`claude-code-router` 不需要传统意义上的“安装”，而是通过 `bunx` 命令直接下载并运行。配置是通过一个 `JSON` 文件完成的。

#### A. 启动服务

您可以在任何目录下打开终端，运行以下命令来启动服务：

```bash
bunx @musistudio/claude-code-router start
```

当您第一次运行此命令时，它会自动下载所需的包。如果看到服务成功启动并监听在 `3456` 端口上的消息，说明第一部分成功了。您可以按 `Ctrl+C` 暂时停止它，因为我们还需要创建配置文件。

> **提示**: 如果您希望服务在后台持续运行，可以使用 `nohup ... &` (在 Linux/macOS 上)。
> `nohup bunx @musistudio/claude-code-router start &`

#### B. 创建配置文件

这是最关键的一步，您需要告诉路由服务您有哪些模型的 API Key 以及如何使用它们。

1.  **创建配置文件夹**:
    在您的**用户主目录**下，创建一个名为 `.claude-code-router` 的文件夹。
    *   **macOS / Linux**: 在终端运行 `mkdir -p ~/.claude-code-router`
    *   **Windows**: 打开文件资源管理器，在地址栏输入 `%USERPROFILE%` 并回车，然后在这里创建一个名为 `.claude-code-router` 的文件夹。

2.  **创建 `config.json` 文件**:
    在刚刚创建的 `.claude-code-router` 文件夹中，创建一个名为 `config.json` 的文本文件。

3.  **编辑配置文件**:
    将下面的模板内容复制到您的 `config.json` 文件中，并根据您的实际情况进行修改。

    **配置模板 `config.json`:**
    ```json
    {
      "log": true,
      "Providers": [
        {
          "provider": "deepseek",
          "api_base_url": "https://api.deepseek.com/v1",
          "api_key": "sk-xxxxxxxxxxxxxxxxxxxxxxxx", // <-- 替换成您的 DeepSeek API Key
          "models": ["deepseek-reasoner", "deepseek-chat"]
        },
        {
          "provider": "openrouter",
          "api_base_url": "https://openrouter.ai/api/v1",
          "api_key": "sk-or-v1-xxxxxxxxxxxxxxxxxxxx", // <-- 替换成您的 OpenRouter API Key
          "models": ["google/gemini-pro-2.5-pro-preview", "anthropic/claude-3.5-sonnet"]
        },
        {
          "provider": "ollama", // 如果您在本地运行 Ollama
          "api_base_url": "http://localhost:11434/v1",
          "api_key": "ollama", // 固定写 "ollama" 即可
          "models": ["qwen:7b-chat", "llama3"]
        }
      ],
      "Router": {
        "background": "ollama,qwen:7b-chat",
        "think": "deepseek,deepseek-reasoner",
        "longContext": "openrouter,google/gemini-pro-2.5-pro-preview",
        "default": "openrouter,anthropic/claude-3.5-sonnet" // 可以设置一个默认模型
      }
    }
    ```

    **修改说明**:
    *   `api_key`: **必须**将 `"sk-xxxxxxxx..."` 替换为您自己从 DeepSeek、OpenRouter 等服务申请的真实 API Key。
    *   `Providers`: 您可以根据需要添加或删除提供商。如果您不用 OpenRouter，就可以删除那一段。
    *   `Router`: 这里定义了规则。例如，上面的配置意味着“思考”任务 (`think`) 会被发送到 DeepSeek，而长文本任务 (`longContext`) 会被发送到 OpenRouter 上的 Gemini。

完成配置后，再次运行 `bunx @musistudio/claude-code-router start` 启动服务。现在，您的智能路由已经准备就绪了。

---

### 第 3 步：在具体场景中使用 (以 GitHub Actions 为例)

现在，您需要让您的AI工具（例如 GitHub Copilot、Cursor、或者文档中提到的 GitHub Actions）不直接访问官方API，而是访问您在本地 `3456` 端口上运行的服务。

文档中给出的 **GitHub Actions** 是一个非常好的例子，它展示了如何自动化地使用这个路由。

1.  **在您的 GitHub 仓库中创建工作流文件**:
    在您的项目仓库里，创建 `.github/workflows/claude.yaml` 文件。

2.  **将以下内容粘贴到 `claude.yaml` 文件中**:
    ```yaml
    name: Claude Code

    on:
      issue_comment:
        types: [created]

    jobs:
      claude:
        # 当评论中包含 '@claude' 时触发
        if: (github.event_name == 'issue_comment' && contains(github.event.comment.body, '@claude'))
        runs-on: ubuntu-latest
        permissions:
          contents: write
          pull-requests: write
          issues: write
        steps:
          - name: Checkout repository
            uses: actions/checkout@v4
          
          # 步骤1: 准备环境和配置
          - name: Prepare Environment
            run: |
              curl -fsSL https://bun.sh/install | bash
              mkdir -p $HOME/.claude-code-router
              cat << 'EOF' > $HOME/.claude-code-router/config.json
              {
                "log": true,
                "OPENAI_API_KEY": "${{ secrets.DEEPSEEK_API_KEY }}",
                "OPENAI_BASE_URL": "https://api.deepseek.com",
                "OPENAI_MODEL": "deepseek-chat"
              }
              EOF
            shell: bash
            
          # 步骤2: 在 GitHub 的服务器上启动路由服务
          - name: Start Claude Code Router
            run: |
              nohup ~/.bun/bin/bunx @musistudio/claude-code-router@1.0.8 start &
            shell: bash

          # 步骤3: 运行 Claude Code，并将其指向刚启动的路由服务
          - name: Run Claude Code
            uses: anthropics/claude-code-action@beta
            env:
              # 关键：指向在同一个运行器上启动的本地路由服务
              ANTHROPIC_BASE_URL: http://localhost:3456
            with:
              # 这里的 key 可以是任意值，因为验证由你自己的服务处理
              anthropic_api_key: "test"
    ```

3.  **在 GitHub 中设置 Secrets**:
    *   进入您的 GitHub 仓库页面，点击 `Settings` > `Secrets and variables` > `Actions`。
    *   点击 `New repository secret`。
    *   创建一个名为 `DEEPSEEK_API_KEY` 的 Secret，并把您的 DeepSeek API Key 粘贴进去。

**工作流程解释**:
当您在仓库的任何一个 Issue 下评论 `@claude do something` 时，这个 GitHub Action 就会被触发。它会在 GitHub 的服务器上自动完成：安装 Bun -> 用您的 Secret 创建配置文件 -> 启动路由服务 -> 运行 Claude Code 并将请求发给这个路由。

这样，您就实现了一个全自动、低成本的 AI 编程助手。

***
*信息来源: [musistudio/claude-code-router on GitHub](https://github.com/musistudio/claude-code-router)*

---

_**User**_

错误

---

_**Assistant**_

<thought>**Pinpointing the Issue**

I've hit a snag with the `npm install` for `@anthropic-ai/claude-code`. The error message is blunt: Windows is a no-go for this package. It seems Claude Code demands macOS or Linux environments. It's a quick realization, but still requires a change in strategy.


**Analyzing the Conflict**

I've been going through the error log in detail. It's crystal clear that `@anthropic-ai/claude-code` isn't compatible with Windows environments, as it needs macOS or Linux. This explains the `npm` installation failure. I've also identified a crucial distinction: the earlier instructions were for `@musistudio/claude-code-router`, which is Windows-friendly via `bun`. To move forward, I'll recommend sticking with the router installation instead of attempting a direct installation of the official package.


**Clarifying the Source**

I'm now fully focused on the distinction between the official `claude-code` package and the `claude-code-router`. It's crucial the user understands that the installation failure stems from an attempt to directly install the former on Windows. The solution involves reverting to the router approach, which is designed to be Windows-compatible through `bun`. I aim to clearly articulate that the router provides a viable alternative, leveraging its architecture to circumvent platform limitations. The GitHub Actions scenario highlighted in the router documentation strengthens its relevance here.


**Refocusing on the Solution**

The key lies in understanding the distinction between `@anthropic-ai/claude-code` and `@musistudio/claude-code-router`. The user's attempt to globally install the official package directly on Windows won't work. The router provides the compatible pathway.  My plan is to redirect the user to the router's installation steps, emphasizing its compatibility with Windows via `bun` to avoid further confusion.


**Resolving the Misunderstanding**

I've clarified the error. The core issue is an attempt to install the official, Windows-incompatible `@anthropic-ai/claude-code`. I've reinforced that the previous instructions were for the `@musistudio/claude-code-router`, which runs on Windows via `bun`. I now understand the user's focus on a "global install" for `@anthropic-ai/claude-code`, which is not needed or possible on their Windows environment. I'm focusing on gently re-directing them back to the router's steps, emphasizing the router's architecture for handling requests, as a solution.


**Correcting the Approach**

I see the issue now. The user is trying to globally install the official `claude-code` package, which is incompatible with Windows. The previous instructions were for the router, designed for Windows compatibility. I need to steer them back to that solution, emphasizing that the router is the right approach.

\n\n**Clarifying the Source**

I've determined the core problem. The user mistakenly attempted a global install of `@anthropic-ai/claude-code`, which won't work on Windows. I need to clarify that the previous instructions focused on `@musistudio/claude-code-router`, which is Windows-compatible via Bun. I'll re-direct them to the router installation process.


</thought>您好，我看到您遇到的这个错误了。

这个错误的原因非常明确，正如错误日志中关键的一行所指出的：
```
npm error Error: Claude Code is not supported on Windows.
npm error Claude Code requires macOS or Linux to run properly.
```
**简单来说，Anthropic 官方的 `claude-code` 工具本身不支持直接在 Windows 的 PowerShell 或 CMD 环境下安装和运行。**

---

这里可能有一个小小的误会。我们之前讨论的 `claude-code-router` 和您现在尝试安装的 `@anthropic-ai/claude-code` 是两个不同的东西：

1.  **`@anthropic-ai/claude-code`**: 这是 Anthropic **官方**的工具包。它有严格的系统限制，如错误所示，它不支持 Windows。
2.  **`@musistudio/claude-code-router`**: 这是我们之前讨论的那个**第三方路由工具**。它的作用是**模拟**一个 Claude Code 的后端服务，然后把收到的请求转发给您在 `config.json` 文件里配置的其他大模型（比如 DeepSeek）。这个工具**是可以在 Windows 上通过 `Bun` 运行的**。

### **解决方案**

您**完全不需要**在您的 Windows 电脑上运行 `npm install -g @anthropic-ai/claude-code` 这个命令，可以直接跳过这一步。

您只需要按照我上一条消息中的指导，完成以下操作即可：

1.  **安装 Bun**: 确保 Bun 已经在您的 Windows PowerShell 中安装成功。
2.  **配置 `config.json`**: 在您的用户目录下创建 `.claude-code-router/config.json` 文件并填入您的 API Keys。
3.  **启动路由服务**: 在您的 PowerShell 终端中运行 `bunx @musistudio/claude-code-router start`。

只要这个路由服务能在您的 Windows 电脑上成功运行，您就已经完成了所有的准备工作。之后的使用（例如通过 GitHub Actions）会去连接您在本地启动的这个服务，而不需要您的电脑本身能运行官方的 `claude-code`。

---

