# Requirements Document

## Introduction

This feature will create an intelligent image analysis tool that examines e-commerce product images (specifically fitness equipment) and generates detailed AI drawing prompts. The tool will analyze visual elements, composition, lighting, and product characteristics to create prompts that can be easily copied and used with various AI image generation tools like Midjourney, DALL-E, and Stable Diffusion.

## Requirements

### Requirement 1

**User Story:** As an e-commerce seller, I want to upload a product image and receive detailed AI drawing prompts, so that I can generate similar high-quality product images for my listings.

#### Acceptance Criteria

1. WHEN a user uploads a product image THEN the system SHALL analyze the image and identify the main product type, quantity, and arrangement
2. WHEN the analysis is complete THEN the system SHALL extract material characteristics, surface treatments, and design details
3. WHEN generating prompts THEN the system SHALL provide both English and Chinese versions for different AI tools
4. WHEN displaying results THEN the system SHALL format all prompts in markdown code blocks for easy copying

### Requirement 2

**User Story:** As a content creator, I want comprehensive visual analysis of product images, so that I can understand the composition and lighting techniques used in successful e-commerce photos.

#### Acceptance Criteria

1. WHEN analyzing an image THEN the system SHALL identify composition layout, viewing angle, and spatial relationships
2. WHEN processing lighting THEN the system SHALL detect light direction, intensity, highlights, shadows, and reflections
3. WHEN examining colors THEN the system SHALL extract primary colors, background tones, and contrast relationships
4. WHEN analyzing backgrounds THEN the system SHALL classify background types and their relationship to the product

### Requirement 3

**User Story:** As an AI art generator user, I want optimized prompts with proper parameters, so that I can generate commercial-quality e-commerce product images efficiently.

#### Acceptance Criteria

1. WHEN generating prompts THEN the system SHALL include specific material descriptions, lighting effects, and composition details
2. WHEN creating negative prompts THEN the system SHALL specify elements to avoid like blur, distortion, or unwanted objects
3. WHEN providing parameters THEN the system SHALL suggest appropriate aspect ratios, style settings, and quality parameters
4. WHEN formatting output THEN the system SHALL organize prompts in clearly labeled markdown code blocks

### Requirement 4

**User Story:** As a fitness equipment seller, I want text element recognition in product images, so that I can understand and replicate successful marketing messaging in my generated images.

#### Acceptance Criteria

1. WHEN text is present in images THEN the system SHALL identify Chinese and English text elements
2. WHEN analyzing text THEN the system SHALL extract product names, selling points, and key information
3. WHEN describing text THEN the system SHALL note position, size, color, and styling of text elements
4. IF no text is present THEN the system SHALL focus entirely on visual product characteristics

### Requirement 5

**User Story:** As a user working with multiple AI platforms, I want platform-specific prompt formatting, so that I can use the generated prompts across different AI image generation services without modification.

#### Acceptance Criteria

1. WHEN generating prompts THEN the system SHALL provide formats compatible with Midjourney, DALL-E, and Stable Diffusion
2. WHEN creating parameters THEN the system SHALL include platform-specific syntax like --ar, --style, --q for Midjourney
3. WHEN organizing output THEN the system SHALL separate main prompts, negative prompts, and parameters into distinct code blocks
4. WHEN providing Chinese versions THEN the system SHALL format them for domestic AI image generation tools