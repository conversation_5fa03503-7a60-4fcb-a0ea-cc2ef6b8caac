# Design Document

## Overview

The Image Prompt Extractor is a specialized AI prompt engineering tool designed to analyze e-commerce product images and generate optimized AI drawing prompts. The system will improve upon the existing prompt structure by providing better formatting, enhanced copyability, and platform-specific optimizations for various AI image generation tools.

## Architecture

### Core Components

1. **Image Analysis Engine**: Processes uploaded images to extract visual characteristics
2. **Prompt Generation System**: Creates structured, platform-specific AI drawing prompts
3. **Output Formatter**: Organizes results in markdown code blocks for easy copying
4. **Template Manager**: Maintains prompt templates for different AI platforms

### Data Flow

```
Image Upload → Visual Analysis → Feature Extraction → Prompt Generation → Formatted Output
```

## Components and Interfaces

### 1. Image Analysis Engine

**Purpose**: Analyze product images and extract key visual characteristics

**Key Functions**:
- Product identification and classification
- Material and texture analysis
- Composition and layout detection
- Color palette extraction
- Lighting and shadow analysis
- Text element recognition (OCR)

**Input**: Product image file
**Output**: Structured analysis data object

### 2. Prompt Generation System

**Purpose**: Convert analysis data into optimized AI drawing prompts

**Key Functions**:
- Generate platform-specific prompt syntax
- Create positive and negative prompts
- Suggest optimal parameters for each platform
- Provide multilingual prompt versions

**Supported Platforms**:
- Midjourney (with --ar, --style, --q parameters)
- DALL-E (OpenAI format)
- Stable Diffusion (with negative prompts)
- Chinese AI tools (文心一格, 通义万相)

### 3. Output Formatter

**Purpose**: Structure results for optimal user experience and copyability

**Key Features**:
- Markdown code block formatting
- Platform-specific sections
- Copy-friendly organization
- Clear labeling and separation

## Data Models

### AnalysisResult
```typescript
interface AnalysisResult {
  productInfo: {
    type: string;
    quantity: number;
    materials: string[];
    keyFeatures: string[];
  };
  composition: {
    layout: string;
    viewAngle: string;
    screenRatio: string;
    arrangement: string;
  };
  colorAndLighting: {
    primaryColors: string[];
    lightingDirection: string;
    shadowEffects: string[];
    backgroundStyle: string;
  };
  textElements?: {
    chineseText: string[];
    englishText: string[];
    positioning: string[];
  };
}
```

### PromptOutput
```typescript
interface PromptOutput {
  analysis: AnalysisResult;
  prompts: {
    midjourney: {
      positive: string;
      negative: string;
      parameters: string;
    };
    dalle: {
      prompt: string;
      style: string;
    };
    stableDiffusion: {
      positive: string;
      negative: string;
      settings: string;
    };
    chinese: {
      prompt: string;
      platform: string;
    };
  };
}
```

## Enhanced Output Format Design

The improved output format will use markdown code blocks for each prompt type, making them easily copyable:

### Structure:
1. **Analysis Summary** (readable format)
2. **Midjourney Prompt** (in code block)
3. **DALL-E Prompt** (in code block)
4. **Stable Diffusion Prompt** (in code block)
5. **Chinese AI Prompt** (in code block)
6. **Quick Copy Section** (all prompts in one block)

### Example Output Format:
```markdown
## 图片分析结果
[Analysis details in readable format]

## AI绘图提示词

### Midjourney
```
[Optimized Midjourney prompt with parameters]
```

### DALL-E
```
[DALL-E specific prompt]
```

### Stable Diffusion
```
Positive: [positive prompt]
Negative: [negative prompt]
```

### 中文AI工具
```
[Chinese prompt for domestic tools]
```

### 一键复制 (Quick Copy)
```
[All prompts formatted for quick access]
```
```

## Error Handling

### Image Processing Errors
- Invalid file format handling
- Image size limitations
- Corrupted file detection
- Unsupported content types

### Analysis Failures
- Fallback to basic analysis when advanced features fail
- Graceful degradation for unclear images
- Default prompt generation for unrecognized products

### Output Generation Issues
- Template fallbacks for missing data
- Validation of generated prompts
- Character limit handling for different platforms

## Testing Strategy

### Unit Testing
- Image analysis component testing
- Prompt generation logic validation
- Output formatting verification
- Template rendering tests

### Integration Testing
- End-to-end workflow testing
- Platform compatibility verification
- Multi-language output validation
- Error handling scenarios

### User Acceptance Testing
- Real product image testing
- Generated prompt quality validation
- Copyability and usability testing
- Cross-platform compatibility verification

## Performance Considerations

### Image Processing
- Optimize image analysis algorithms
- Implement caching for repeated analyses
- Set reasonable file size limits
- Use efficient image processing libraries

### Prompt Generation
- Template-based generation for speed
- Minimize computational overhead
- Cache common prompt patterns
- Optimize for multiple platform outputs

## Security and Privacy

### Image Handling
- Secure image upload and processing
- Temporary file cleanup
- No permanent storage of user images
- Input validation and sanitization

### Data Protection
- No collection of personal information
- Secure processing of business-sensitive product images
- Clear data retention policies