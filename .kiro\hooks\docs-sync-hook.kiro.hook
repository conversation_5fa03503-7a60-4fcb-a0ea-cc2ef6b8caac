{"enabled": true, "name": "Documentation Sync", "description": "Listens to all source files and relevant configuration files in the repository and triggers documentation updates when changes occur", "version": "1", "when": {"type": "fileEdited", "patterns": ["*.md", "*.py", "*.txt", "*.pdf"]}, "then": {"type": "askAgent", "prompt": "Source files have been modified in this repository. Please review the changes and update the documentation accordingly. If there's a README.md file, update it to reflect any new functionality, changes in structure, or important information. If there's a /docs folder, update the relevant documentation files there as well. Focus on keeping the documentation accurate, clear, and up-to-date with the current state of the project."}}