import os
import sys
from PIL import Image

def batch_flip_images(root_dir):
    """
    递归地查找目录及其子目录中的所有图像，并水平翻转它们。
    """
    image_extensions = ['.png', '.jpg', '.jpeg']
    print(f"开始处理目录: {root_dir}")
    total_flipped = 0
    total_failed = 0

    for subdir, _, files in os.walk(root_dir):
        for file in files:
            # 检查文件是否具有有效的图像扩展名
            if any(file.lower().endswith(ext) for ext in image_extensions):
                image_path = os.path.join(subdir, file)
                try:
                    # 使用 'with' 语句确保文件被正确关闭
                    with Image.open(image_path) as img:
                        # 水平翻转图像
                        flipped_img = img.transpose(Image.FLIP_LEFT_RIGHT)
                        # 保存图像，覆盖原始文件
                        flipped_img.save(image_path)
                        print(f"已翻转: {image_path}")
                        total_flipped += 1
                except Exception as e:
                    print(f"处理失败 {image_path}: {e}")
                    total_failed += 1
    
    print("\n--- 处理完成 ---")
    print(f"成功翻转 {total_flipped} 张图片。")
    print(f"失败 {total_failed} 张图片。")


if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("用法: python batch_flip_images.py <要处理的文件夹路径>")
        sys.exit(1)

    target_directory = sys.argv[1]

    if not os.path.isdir(target_directory):
         print(f"错误: 目录 '{target_directory}' 不存在或不是一个有效的目录。")
         sys.exit(1)
    else:
        batch_flip_images(target_directory) 