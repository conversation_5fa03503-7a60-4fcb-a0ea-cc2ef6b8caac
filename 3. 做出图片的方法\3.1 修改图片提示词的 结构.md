太好了，我们来**逐句分析这个提示词的写法和结构**，并总结出一个**通用模板**，以后你可以高效、稳定地用这个结构来修改类似的电商图细节。

---

## ✅ 优秀提示词结构（示例）

> **/edit 保持图片整体比例为 1:1，整体背景、文字与商品构图不变，仅将右侧银色金属手柄部分水平向右拉长约50%，并适度放大，使其视觉上占据更多空间，延伸至画面边缘之外，增强厚重感和冲击力**

---

## ✍️ 分解结构 + 解释写法逻辑

| 段落           | 内容                 | 功能说明                            | 写法建议                                   |
| ------------ | ------------------ | ------------------------------- | -------------------------------------- |
| ① 总控限定       | `保持图片整体比例为 1:1`    | **防止AI误改比例或格式**，强调是电商主图         | 开头先限定“比例”和“格式”，电商用图必须稳定                |
| ② 保留不变       | `整体背景、文字与商品构图不变`   | **明确告诉AI“不要乱动其他部分”**            | 使用“保持/不变/原样”字样限定范围                     |
| ③ 目标部位       | `仅将右侧银色金属手柄部分`     | **锁定要修改的具体部分 + 特征描述**           | 指出位置（左/右/中）、颜色、材质或功能特征                 |
| ④ 动作行为       | `水平向右拉长约50%，并适度放大` | **说明要做什么改动 + 强度/方向**            | 动作动词：拉长、放大、缩小、旋转、上移、突出等；加上数值如“约50%”更明确 |
| ⑤ 修改目的       | `使其视觉上占据更多空间`      | **告诉AI为什么要这么改**，帮助它做出**合理构图判断** | 可以用“增强XXX感”/“形成视觉主导”等词引导构图结果           |
| ⑥ 空间表达       | `延伸至画面边缘之外`        | **强调范围和视觉张力方向**                 | “延伸”“出框”“覆盖到X区域”可用于加强冲击力               |
| ⑦ 额外风格指令（可选） | `增强厚重感和冲击力`        | **强调风格/质感/气场等**，利于电商主图氛围塑造      | 用“突出金属感”“增强视觉重量感”“更具专业氛围”等补充语言风格       |

---

## 🧩 通用可复用模板（适合 `/edit` 电商主图修改）

```
/edit 保持图片整体比例为 1:1，其他构图与背景不变，仅将[位置描述+商品部位]进行[操作动作+数值]，使其在画面中[占比/位置变化]，形成[视觉目的]，整体风格保持一致
```

---

## 🧪 示例再造（不同修改需求）

1. **放大整个产品但不遮挡文字**

   ```
   /edit 保持1:1比例与文字位置不变，将商品整体放大至原尺寸的120%，保持商品完整不裁边，增强视觉冲击力
   ```

2. **移动品牌文字位置**

   ```
   /edit 将顶部品牌文字上移100px，避免遮挡商品主体，保持其他构图不变
   ```

3. **加强金属质感**

   ```
   /edit 保持构图和比例不变，增强银色手柄部分的金属光泽和高亮反射，使其质感更加真实专业
   ```

---

如果你愿意，我可以给你做一个“一键生成图片修改提示词”的小助手，只需输入修改目标和部位，它就自动帮你生成 `/edit` 提示词。是否需要我为你做这个工具或模板？
